1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
4    android:versionCode="43"
5    android:versionName="1.2.0.20250624" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
13-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:7:5-77
13-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
14-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:8:5-95
14-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:8:22-92
15    <uses-permission android:name="android.permission.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
15-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:9:5-101
15-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:9:22-99
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
16-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:10:5-76
16-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:10:22-74
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
17-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:11:5-88
17-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:11:22-86
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:12:5-65
18-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:12:22-63
19    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
19-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:13:5-78
19-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:13:22-75
20    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
20-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:14:5-81
20-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:14:22-78
21    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
21-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:15:5-16:47
21-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:15:22-75
22
23    <permission
23-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:18:5-20:47
24        android:name="com.tqhit.battery.one.permission.FINISH_OVERLAY"
24-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:19:9-71
25        android:protectionLevel="signature" />
25-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:20:9-44
26
27    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
27-->[com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:32:5-76
27-->[com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:32:22-73
28    <uses-permission android:name="android.permission.WAKE_LOCK" />
28-->[com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:33:5-68
28-->[com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:33:22-65
29    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
29-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:26:5-79
29-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:26:22-76
30    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
30-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:27:5-82
30-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:27:22-79
31    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
31-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:28:5-88
31-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:28:22-85
32    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
32-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:29:5-83
32-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:29:22-80
33    <queries>
33-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:35:5-68:15
34
35        <!-- For browser content -->
36        <intent>
36-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:38:9-44:18
37            <action android:name="android.intent.action.VIEW" />
37-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:17-69
37-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:25-66
38
39            <category android:name="android.intent.category.BROWSABLE" />
39-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:91:17-78
39-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:91:27-75
40
41            <data android:scheme="https" />
41-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
41-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:23-47
42        </intent>
43        <!-- End of browser content -->
44        <!-- For CustomTabsService -->
45        <intent>
45-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:47:9-49:18
46            <action android:name="android.support.customtabs.action.CustomTabsService" />
46-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:48:13-90
46-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:48:21-87
47        </intent>
48        <!-- End of CustomTabsService -->
49        <!-- For MRAID capabilities -->
50        <intent>
50-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:52:9-56:18
51            <action android:name="android.intent.action.INSERT" />
51-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:53:13-67
51-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:53:21-64
52
53            <data android:mimeType="vnd.android.cursor.dir/event" />
53-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
54        </intent>
55        <intent>
55-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:57:9-61:18
56            <action android:name="android.intent.action.VIEW" />
56-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:17-69
56-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:25-66
57
58            <data android:scheme="sms" />
58-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
58-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:23-47
59        </intent>
60        <intent>
60-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:62:9-66:18
61            <action android:name="android.intent.action.DIAL" />
61-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:63:13-65
61-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:63:21-62
62
63            <data android:path="tel:" />
63-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
64        </intent>
65        <!-- End of MRAID capabilities -->
66        <intent>
66-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:8:9-10:18
67            <action android:name="com.applovin.am.intent.action.APPHUB_SERVICE" />
67-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:9:13-83
67-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:9:21-80
68        </intent>
69    </queries>
70
71    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
71-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
71-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
72    <uses-permission android:name="com.applovin.array.apphub.permission.BIND_APPHUB_SERVICE" />
72-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:20:5-96
72-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:20:22-93
73
74    <permission
74-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
75        android:name="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
75-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
76        android:protectionLevel="signature" />
76-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
77
78    <uses-permission android:name="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
78-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
78-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
79
80    <application
80-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:22:5-132:19
81        android:name="com.tqhit.battery.one.BatteryApplication"
81-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:23:9-64
82        android:allowBackup="true"
82-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:24:9-35
83        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
83-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
84        android:dataExtractionRules="@xml/data_extraction_rules"
84-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:25:9-65
85        android:debuggable="true"
86        android:extractNativeLibs="false"
87        android:fullBackupContent="@xml/backup_rules"
87-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:26:9-54
88        android:icon="@mipmap/ic_launcher"
88-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:27:9-43
89        android:label="@string/app_name"
89-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:28:9-41
90        android:roundIcon="@mipmap/ic_launcher_round"
90-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:29:9-54
91        android:screenOrientation="portrait"
91-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:30:9-45
92        android:supportsRtl="true"
92-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:31:9-35
93        android:theme="@style/Theme.BatteryOne" >
93-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:32:9-48
94        <!-- Sample AdMob app ID: ca-app-pub-3940256099942544~3347511713 -->
95        <meta-data
95-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:35:9-37:70
96            android:name="com.google.android.gms.ads.APPLICATION_ID"
96-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:36:13-69
97            android:value="ca-app-pub-9844172086883515~3386117176" />
97-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:37:13-67
98
99        <activity
99-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:39:9-47:20
100            android:name="com.tqhit.battery.one.activity.splash.SplashActivity"
100-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:40:13-80
101            android:exported="true"
101-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:41:13-36
102            android:theme="@style/Theme.BatteryOne.Splash" >
102-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:42:13-59
103            <intent-filter>
103-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:43:13-46:29
104                <action android:name="android.intent.action.MAIN" />
104-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:44:17-69
104-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:44:25-66
105
106                <category android:name="android.intent.category.LAUNCHER" />
106-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:45:17-77
106-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:45:27-74
107            </intent-filter>
108        </activity>
109        <activity
109-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:49:9-53:54
110            android:name="com.tqhit.battery.one.activity.starting.StartingActivity"
110-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:50:13-84
111            android:exported="false"
111-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:51:13-37
112            android:screenOrientation="portrait"
112-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:52:13-49
113            android:theme="@style/Theme.BatteryOne" />
113-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:53:13-52
114        <activity
114-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:55:9-59:51
115            android:name="com.tqhit.battery.one.activity.main.MainActivity"
115-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:56:13-76
116            android:exported="true"
116-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:57:13-36
117            android:launchMode="singleTask"
117-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:58:13-44
118            android:screenOrientation="portrait" />
118-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:59:13-49
119        <activity
119-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:61:9-65:51
120            android:name="com.tqhit.battery.one.activity.animation.AnimationActivity"
120-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:62:13-86
121            android:configChanges="orientation|screenSize"
121-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:63:13-59
122            android:launchMode="singleTask"
122-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:64:13-44
123            android:screenOrientation="portrait" />
123-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:65:13-49
124        <activity
124-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:67:9-73:51
125            android:name="com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity"
125-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:68:13-90
126            android:configChanges="orientation|screenSize"
126-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:71:13-59
127            android:exported="false"
127-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:69:13-37
128            android:launchMode="singleTask"
128-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:72:13-44
129            android:screenOrientation="portrait"
129-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:73:13-49
130            android:theme="@style/Theme.AppCompat.NoActionBar" />
130-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:70:13-63
131        <activity
131-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:75:9-81:51
132            android:name="com.tqhit.battery.one.activity.password.EnterPasswordActivity"
132-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:76:13-89
133            android:configChanges="orientation|screenSize"
133-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:79:13-59
134            android:exported="false"
134-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:77:13-37
135            android:launchMode="singleTask"
135-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:80:13-44
136            android:screenOrientation="portrait"
136-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:81:13-49
137            android:theme="@style/Theme.AppCompat.NoActionBar" />
137-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:78:13-63
138        <activity
138-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:83:9-94:20
139            android:name="com.tqhit.battery.one.features.new_discharge.presentation.TestNewDischargeActivity"
139-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:84:13-110
140            android:exported="true"
140-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:85:13-36
141            android:screenOrientation="portrait"
141-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:87:13-49
142            android:theme="@style/Theme.AppCompat" >
142-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:86:13-51
143            <intent-filter>
143-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:88:13-93:29
144                <action android:name="android.intent.action.VIEW" />
144-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:17-69
144-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:25-66
145
146                <category android:name="android.intent.category.DEFAULT" />
146-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:90:17-76
146-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:90:27-73
147                <category android:name="android.intent.category.BROWSABLE" />
147-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:91:17-78
147-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:91:27-75
148
149                <data
149-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
150                    android:host="test_discharge"
150-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:48-77
151                    android:scheme="battery" />
151-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:23-47
152            </intent-filter>
153        </activity> <!-- Legacy TestNewChargeActivity removed - was in legacy directory -->
154        <!-- DebugActivity will be conditionally included via build variant manifests -->
155        <service
155-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:100:9-104:59
156            android:name="com.tqhit.battery.one.service.BatteryMonitorService"
156-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:101:13-79
157            android:enabled="true"
157-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:102:13-35
158            android:exported="false"
158-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:103:13-37
159            android:foregroundServiceType="specialUse" />
159-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:104:13-55
160        <service
160-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:105:9-109:59
161            android:name="com.tqhit.battery.one.service.ChargingOverlayService"
161-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:106:13-80
162            android:enabled="true"
162-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:107:13-35
163            android:exported="false"
163-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:108:13-37
164            android:foregroundServiceType="specialUse" /> <!-- Legacy services removed - replaced by CoreBatteryStatsService and UnifiedBatteryNotificationService -->
164-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:109:13-55
165        <!-- NewChargeMonitorService, DischargeTimerService, and BatteryStatusService were in legacy directory -->
166        <service
166-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:112:9-116:59
167            android:name="com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService"
167-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:113:13-112
168            android:enabled="true"
168-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:114:13-35
169            android:exported="false"
169-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:115:13-37
170            android:foregroundServiceType="specialUse" />
170-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:116:13-55
171        <service
171-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:117:9-121:59
172            android:name="com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService"
172-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:118:13-108
173            android:enabled="true"
173-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:119:13-35
174            android:exported="false"
174-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:120:13-37
175            android:foregroundServiceType="specialUse" />
175-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:121:13-55
176        <service
176-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:122:9-126:59
177            android:name="com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService"
177-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:123:13-112
178            android:enabled="true"
178-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:124:13-35
179            android:exported="false"
179-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:125:13-37
180            android:foregroundServiceType="specialUse" />
180-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:126:13-55
181        <service
181-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:127:9-131:59
182            android:name="com.tqhit.battery.one.service.optimization.MemoryOptimizationService"
182-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:128:13-96
183            android:enabled="true"
183-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:129:13-35
184            android:exported="false"
184-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:130:13-37
185            android:foregroundServiceType="specialUse" />
185-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:131:13-55
186
187        <activity
187-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:10:9-12:49
188            android:name="cat.ereza.customactivityoncrash.activity.DefaultErrorActivity"
188-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:11:13-89
189            android:process=":error_activity" />
189-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:12:13-46
190
191        <provider
191-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:14:9-18:39
192            android:name="cat.ereza.customactivityoncrash.provider.CaocInitProvider"
192-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:15:13-85
193            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.customactivityoncrashinitprovider"
193-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:16:13-85
194            android:exported="false"
194-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:17:13-37
195            android:initOrder="101" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
195-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:18:13-36
196        <activity
196-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:73:9-78:43
197            android:name="com.google.android.gms.ads.AdActivity"
197-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:74:13-65
198            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
198-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:75:13-122
199            android:exported="false"
199-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:76:13-37
200            android:theme="@android:style/Theme.Translucent" />
200-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:77:13-61
201
202        <provider
202-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:80:9-85:43
203            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
203-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:81:13-76
204            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.mobileadsinitprovider"
204-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:82:13-73
205            android:exported="false"
205-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:83:13-37
206            android:initOrder="100" />
206-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:84:13-36
207
208        <service
208-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:87:9-91:43
209            android:name="com.google.android.gms.ads.AdService"
209-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:88:13-64
210            android:enabled="true"
210-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:89:13-35
211            android:exported="false" />
211-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:90:13-37
212
213        <activity
213-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:93:9-97:43
214            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
214-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:94:13-82
215            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
215-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:95:13-122
216            android:exported="false" />
216-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:96:13-37
217        <activity
217-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:98:9-105:43
218            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
218-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:99:13-82
219            android:excludeFromRecents="true"
219-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:100:13-46
220            android:exported="false"
220-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:101:13-37
221            android:launchMode="singleTask"
221-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:102:13-44
222            android:taskAffinity=""
222-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:103:13-36
223            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
223-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:104:13-72
224
225        <meta-data
225-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:107:9-109:36
226            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
226-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:108:13-79
227            android:value="true" />
227-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:109:13-33
228        <meta-data
228-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:110:9-112:36
229            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
229-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:111:13-83
230            android:value="true" />
230-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e62382538ef1316778b8da0e23e702e3\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:112:13-33
231
232        <receiver
232-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
233            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
233-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
234            android:enabled="true"
234-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
235            android:exported="false" >
235-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
236        </receiver>
237
238        <service
238-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
239            android:name="com.google.android.gms.measurement.AppMeasurementService"
239-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
240            android:enabled="true"
240-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
241            android:exported="false" />
241-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
242        <service
242-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
243            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
243-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
244            android:enabled="true"
244-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
245            android:exported="false"
245-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
246            android:permission="android.permission.BIND_JOB_SERVICE" />
246-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
247        <service
247-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
248            android:name="com.google.firebase.components.ComponentDiscoveryService"
248-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:31:13-84
249            android:directBootAware="true"
249-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
250            android:exported="false" >
250-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:32:13-37
251            <meta-data
251-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
252                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
252-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
253                android:value="com.google.firebase.components.ComponentRegistrar" />
253-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
254            <meta-data
254-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:15:13-17:85
255                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
255-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:16:17-126
256                android:value="com.google.firebase.components.ComponentRegistrar" />
256-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:17:17-82
257            <meta-data
257-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:18:13-20:85
258                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
258-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:19:17-115
259                android:value="com.google.firebase.components.ComponentRegistrar" />
259-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:20:17-82
260            <meta-data
260-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:29:13-31:85
261                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
261-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:30:17-128
262                android:value="com.google.firebase.components.ComponentRegistrar" />
262-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:31:17-82
263            <meta-data
263-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:32:13-34:85
264                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
264-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:33:17-117
265                android:value="com.google.firebase.components.ComponentRegistrar" />
265-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:34:17-82
266            <meta-data
266-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:29:13-31:85
267                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
267-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:30:17-117
268                android:value="com.google.firebase.components.ComponentRegistrar" />
268-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:31:17-82
269            <meta-data
269-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
270                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
270-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
271                android:value="com.google.firebase.components.ComponentRegistrar" />
271-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
272            <meta-data
272-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
273                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
273-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
274                android:value="com.google.firebase.components.ComponentRegistrar" />
274-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
275            <meta-data
275-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
276                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
276-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
277                android:value="com.google.firebase.components.ComponentRegistrar" />
277-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
278            <meta-data
278-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
279                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
279-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
280                android:value="com.google.firebase.components.ComponentRegistrar" />
280-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
281            <meta-data
281-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
282                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
282-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
283                android:value="com.google.firebase.components.ComponentRegistrar" />
283-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
284            <meta-data
284-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
285                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
285-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
286                android:value="com.google.firebase.components.ComponentRegistrar" />
286-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
287        </service>
288
289        <provider
289-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:26:9-30:39
290            android:name="com.applovin.sdk.AppLovinInitProvider"
290-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:27:13-65
291            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.applovininitprovider"
291-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:28:13-72
292            android:exported="false"
292-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:29:13-37
293            android:initOrder="101" /> <!-- Init order is 101 so we're before Firebase/Google which uses 100 -->
293-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:30:13-36
294        <activity
294-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:32:9-39:74
295            android:name="com.applovin.adview.AppLovinFullscreenActivity"
295-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:33:13-74
296            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
296-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:34:13-139
297            android:exported="false"
297-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:35:13-37
298            android:hardwareAccelerated="true"
298-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:36:13-47
299            android:launchMode="singleTop"
299-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:37:13-43
300            android:screenOrientation="behind"
300-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:38:13-47
301            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
301-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:39:13-71
302        <activity
302-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:40:9-42:142
303            android:name="com.applovin.sdk.AppLovinWebViewActivity"
303-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:41:13-68
304            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" /> <!-- Mediation Debugger Activities -->
304-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:42:13-139
305        <activity
305-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:45:9-48:87
306            android:name="com.applovin.mediation.MaxDebuggerActivity"
306-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:46:13-70
307            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
307-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:47:13-139
308            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
308-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:48:13-84
309        <activity
309-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:49:9-52:87
310            android:name="com.applovin.mediation.MaxDebuggerDetailActivity"
310-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:50:13-76
311            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
311-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:51:13-139
312            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
312-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:52:13-84
313        <activity
313-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:53:9-56:87
314            android:name="com.applovin.mediation.MaxDebuggerMultiAdActivity"
314-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:54:13-77
315            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
315-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:55:13-139
316            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
316-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:56:13-84
317        <activity
317-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:57:9-60:87
318            android:name="com.applovin.mediation.MaxDebuggerAdUnitsListActivity"
318-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:58:13-81
319            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
319-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:59:13-139
320            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
320-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:60:13-84
321        <activity
321-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:61:9-64:87
322            android:name="com.applovin.mediation.MaxDebuggerAdUnitWaterfallsListActivity"
322-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:62:13-90
323            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
323-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:63:13-139
324            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
324-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:64:13-84
325        <activity
325-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:65:9-68:87
326            android:name="com.applovin.mediation.MaxDebuggerAdUnitDetailActivity"
326-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:66:13-82
327            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
327-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:67:13-139
328            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
328-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:68:13-84
329        <activity
329-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:69:9-72:87
330            android:name="com.applovin.mediation.MaxDebuggerCmpNetworksListActivity"
330-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:70:13-85
331            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
331-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:71:13-139
332            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
332-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:72:13-84
333        <activity
333-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:73:9-76:87
334            android:name="com.applovin.mediation.MaxDebuggerTcfConsentStatusesListActivity"
334-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:74:13-92
335            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
335-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:75:13-139
336            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
336-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:76:13-84
337        <activity
337-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:77:9-80:87
338            android:name="com.applovin.mediation.MaxDebuggerTcfInfoListActivity"
338-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:78:13-81
339            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
339-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:79:13-139
340            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
340-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:80:13-84
341        <activity
341-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:81:9-84:87
342            android:name="com.applovin.mediation.MaxDebuggerTcfStringActivity"
342-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:82:13-79
343            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
343-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:83:13-139
344            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
344-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:84:13-84
345        <activity
345-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:85:9-88:87
346            android:name="com.applovin.mediation.MaxDebuggerTestLiveNetworkActivity"
346-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:86:13-85
347            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
347-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:87:13-139
348            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
348-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:88:13-84
349        <activity
349-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:89:9-92:87
350            android:name="com.applovin.mediation.MaxDebuggerTestModeNetworkActivity"
350-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:90:13-85
351            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
351-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:91:13-139
352            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
352-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:92:13-84
353        <activity
353-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:93:9-96:87
354            android:name="com.applovin.mediation.MaxDebuggerUnifiedFlowActivity"
354-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:94:13-81
355            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
355-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:95:13-139
356            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
356-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:96:13-84
357        <activity
357-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:97:9-100:87
358            android:name="com.applovin.mediation.MaxDebuggerWaterfallSegmentsActivity"
358-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:98:13-87
359            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
359-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:99:13-139
360            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
360-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:100:13-84
361        <activity
361-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:101:9-104:91
362            android:name="com.applovin.creative.MaxCreativeDebuggerActivity"
362-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:102:13-77
363            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
363-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:103:13-139
364            android:theme="@style/com.applovin.creative.CreativeDebuggerActivity.Theme" />
364-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:104:13-88
365        <activity
365-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:105:9-108:91
366            android:name="com.applovin.creative.MaxCreativeDebuggerDisplayedAdActivity"
366-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:106:13-88
367            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
367-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:107:13-139
368            android:theme="@style/com.applovin.creative.CreativeDebuggerActivity.Theme" /> <!-- Services -->
368-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:108:13-88
369        <service
369-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:111:9-114:44
370            android:name="com.applovin.impl.adview.activity.FullscreenAdService"
370-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:112:13-81
371            android:exported="false"
371-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:113:13-37
372            android:stopWithTask="false" />
372-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:114:13-41
373        <service
373-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:22:9-25:40
374            android:name="com.google.firebase.sessions.SessionLifecycleService"
374-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:23:13-80
375            android:enabled="true"
375-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:24:13-35
376            android:exported="false" />
376-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:25:13-37
377
378        <provider
378-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
379            android:name="com.google.firebase.provider.FirebaseInitProvider"
379-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
380            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.firebaseinitprovider"
380-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
381            android:directBootAware="true"
381-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
382            android:exported="false"
382-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
383            android:initOrder="100" />
383-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
384
385        <activity
385-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
386            android:name="com.google.android.gms.common.api.GoogleApiActivity"
386-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
387            android:exported="false"
387-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
388            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
388-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
389
390        <provider
390-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
391            android:name="androidx.startup.InitializationProvider"
391-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
392            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.androidx-startup"
392-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
393            android:exported="false" >
393-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
394            <meta-data
394-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
395                android:name="androidx.emoji2.text.EmojiCompatInitializer"
395-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
396                android:value="androidx.startup" />
396-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
397            <meta-data
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
398                android:name="androidx.work.WorkManagerInitializer"
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
399                android:value="androidx.startup" />
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
400            <meta-data
400-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7ccc3f972f78556d0585b6ed29d65d8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
401                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
401-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7ccc3f972f78556d0585b6ed29d65d8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
402                android:value="androidx.startup" />
402-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7ccc3f972f78556d0585b6ed29d65d8\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
403            <meta-data
403-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
404                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
404-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
405                android:value="androidx.startup" />
405-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
406        </provider>
407
408        <service
408-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
409            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
409-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
410            android:directBootAware="false"
410-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
411            android:enabled="@bool/enable_system_alarm_service_default"
411-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
412            android:exported="false" />
412-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
413        <service
413-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
414            android:name="androidx.work.impl.background.systemjob.SystemJobService"
414-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
415            android:directBootAware="false"
415-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
416            android:enabled="@bool/enable_system_job_service_default"
416-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
417            android:exported="true"
417-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
418            android:permission="android.permission.BIND_JOB_SERVICE" />
418-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
419        <service
419-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
420            android:name="androidx.work.impl.foreground.SystemForegroundService"
420-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
421            android:directBootAware="false"
421-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
422            android:enabled="@bool/enable_system_foreground_service_default"
422-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
423            android:exported="false" />
423-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
424
425        <receiver
425-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
426            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
426-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
427            android:directBootAware="false"
427-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
428            android:enabled="true"
428-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
429            android:exported="false" />
429-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
430        <receiver
430-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
431            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
431-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
432            android:directBootAware="false"
432-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
433            android:enabled="false"
433-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
434            android:exported="false" >
434-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
435            <intent-filter>
435-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
436                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
436-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
436-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
437                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
437-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
437-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
438            </intent-filter>
439        </receiver>
440        <receiver
440-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
441            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
441-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
442            android:directBootAware="false"
442-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
443            android:enabled="false"
443-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
444            android:exported="false" >
444-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
445            <intent-filter>
445-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
446                <action android:name="android.intent.action.BATTERY_OKAY" />
446-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
446-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
447                <action android:name="android.intent.action.BATTERY_LOW" />
447-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
447-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
448            </intent-filter>
449        </receiver>
450        <receiver
450-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
451            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
451-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
452            android:directBootAware="false"
452-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
453            android:enabled="false"
453-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
454            android:exported="false" >
454-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
455            <intent-filter>
455-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
456                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
456-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
456-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
457                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
457-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
457-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
458            </intent-filter>
459        </receiver>
460        <receiver
460-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
461            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
461-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
462            android:directBootAware="false"
462-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
463            android:enabled="false"
463-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
464            android:exported="false" >
464-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
465            <intent-filter>
465-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
466                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
466-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
466-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
467            </intent-filter>
468        </receiver>
469        <receiver
469-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
470            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
470-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
471            android:directBootAware="false"
471-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
472            android:enabled="false"
472-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
473            android:exported="false" >
473-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
474            <intent-filter>
474-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
475                <action android:name="android.intent.action.BOOT_COMPLETED" />
475-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
475-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
476                <action android:name="android.intent.action.TIME_SET" />
476-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
476-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
477                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
477-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
477-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
478            </intent-filter>
479        </receiver>
480        <receiver
480-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
481            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
481-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
482            android:directBootAware="false"
482-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
483            android:enabled="@bool/enable_system_alarm_service_default"
483-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
484            android:exported="false" >
484-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
485            <intent-filter>
485-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
486                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
486-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
486-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
487            </intent-filter>
488        </receiver>
489        <receiver
489-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
490            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
490-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
491            android:directBootAware="false"
491-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
492            android:enabled="true"
492-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
493            android:exported="true"
493-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
494            android:permission="android.permission.DUMP" >
494-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
495            <intent-filter>
495-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
496                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
496-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
496-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f303ab3acb606cde4f90eae6bf478958\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
497            </intent-filter>
498        </receiver>
499
500        <uses-library
500-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
501            android:name="androidx.window.extensions"
501-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
502            android:required="false" />
502-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
503        <uses-library
503-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
504            android:name="androidx.window.sidecar"
504-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
505            android:required="false" />
505-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
506        <uses-library
506-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79e63424ace2f8e7b3d5cf4517f24058\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
507            android:name="android.ext.adservices"
507-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79e63424ace2f8e7b3d5cf4517f24058\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
508            android:required="false" />
508-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79e63424ace2f8e7b3d5cf4517f24058\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
509
510        <meta-data
510-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1057906e728c9c7b13b7392bddc5c3a4\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
511            android:name="com.google.android.gms.version"
511-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1057906e728c9c7b13b7392bddc5c3a4\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
512            android:value="@integer/google_play_services_version" />
512-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1057906e728c9c7b13b7392bddc5c3a4\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
513
514        <service
514-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
515            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
515-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
516            android:exported="false" >
516-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
517            <meta-data
517-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
518                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
518-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
519                android:value="cct" />
519-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
520        </service>
521
522        <receiver
522-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
523            android:name="androidx.profileinstaller.ProfileInstallReceiver"
523-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
524            android:directBootAware="false"
524-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
525            android:enabled="true"
525-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
526            android:exported="true"
526-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
527            android:permission="android.permission.DUMP" >
527-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
528            <intent-filter>
528-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
529                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
529-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
529-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
530            </intent-filter>
531            <intent-filter>
531-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
532                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
532-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
532-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
533            </intent-filter>
534            <intent-filter>
534-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
535                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
535-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
535-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
536            </intent-filter>
537            <intent-filter>
537-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
538                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
538-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
538-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
539            </intent-filter>
540        </receiver>
541
542        <service
542-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
543            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
543-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
544            android:exported="false"
544-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
545            android:permission="android.permission.BIND_JOB_SERVICE" >
545-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
546        </service>
547
548        <receiver
548-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
549            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
549-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
550            android:exported="false" />
550-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
551
552        <service
552-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bebd5b5ae125afd42a8ef50c4abc08a0\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
553            android:name="androidx.room.MultiInstanceInvalidationService"
553-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bebd5b5ae125afd42a8ef50c4abc08a0\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
554            android:directBootAware="true"
554-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bebd5b5ae125afd42a8ef50c4abc08a0\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
555            android:exported="false" />
555-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bebd5b5ae125afd42a8ef50c4abc08a0\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
556
557        <provider
557-->[com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:11:9-14:40
558            android:name="com.adjust.sdk.SystemLifecycleContentProvider"
558-->[com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:12:13-73
559            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.adjust-lifecycle-provider"
559-->[com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:13:13-77
560            android:exported="false" />
560-->[com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:14:13-37
561    </application>
562
563</manifest>
