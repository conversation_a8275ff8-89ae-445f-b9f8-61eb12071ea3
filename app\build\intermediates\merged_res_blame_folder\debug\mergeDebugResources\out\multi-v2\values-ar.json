{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-99:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "156,157,158,159,160,161,162,163,165,166,167,168,169,170,171,172,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11960,12064,12208,12330,12435,12573,12701,12812,13044,13181,13285,13435,13557,13696,13842,13906,13972", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "12059,12203,12325,12430,12568,12696,12807,12909,13176,13280,13430,13552,13691,13837,13901,13967,14051"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,11,19,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,349,849,1314,1393,1471,1547,1641,1733,1807,1872,1964,2054,2124,2188,2251,2320,2428,2537,2652,2718,2801,2873,2945,3037,3128,3192,3255,3308,3379,3434,3495,3553,3627,3691,3755,3815,3880,3944,3996,4053,4124,4195,4251", "endLines": "10,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,51,56,70,70,55,67", "endOffsets": "344,844,1309,1388,1466,1542,1636,1728,1802,1867,1959,2049,2119,2183,2246,2315,2423,2532,2647,2713,2796,2868,2940,3032,3123,3187,3250,3303,3374,3429,3490,3548,3622,3686,3750,3810,3875,3939,3991,4048,4119,4190,4246,4314"}, "to": {"startLines": "2,11,19,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,444,944,19503,19582,19660,19736,19830,19922,19996,20061,20153,20243,20313,20377,20440,20509,20617,20726,20841,20907,20990,21062,21134,21226,21317,21381,22140,22193,22264,22319,22380,22438,22512,22576,22640,22700,22765,22829,22881,22938,23009,23080,23136", "endLines": "10,18,26,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,51,56,70,70,55,67", "endOffsets": "439,939,1404,19577,19655,19731,19825,19917,19991,20056,20148,20238,20308,20372,20435,20504,20612,20721,20836,20902,20985,21057,21129,21221,21312,21376,21439,22188,22259,22314,22375,22433,22507,22571,22635,22695,22760,22824,22876,22933,23004,23075,23131,23199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3f93ad0b3932b2c8bca088b137c6ec88\\transformed\\jetified-play-services-ads-24.2.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,288,573,634,755,871,1006,1059,1116,1228,1313,1351,1430,1462,1493,1536,1604,1644", "endColumns": "40,47,53,60,120,115,134,52,56,111,84,37,78,31,30,42,67,39,55", "endOffsets": "239,287,341,633,754,870,1005,1058,1115,1227,1312,1350,1429,1461,1492,1535,1603,1643,1699"}, "to": {"startLines": "440,441,442,460,461,462,463,464,465,466,467,499,500,501,502,503,504,505,556", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "37933,37978,38030,39366,39431,39556,39676,39815,39872,39933,40049,42516,42558,42641,42677,42712,42759,42831,46579", "endColumns": "44,51,57,64,124,119,138,56,60,115,88,41,82,35,34,46,71,43,59", "endOffsets": "37973,38025,38083,39426,39551,39671,39810,39867,39928,40044,40133,42553,42636,42672,42707,42754,42826,42870,46634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "128,129,130,131,132,133,134,531", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "9732,9825,9927,10022,10125,10228,10330,44849", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "9820,9922,10017,10120,10223,10325,10439,44945"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "164", "startColumns": "4", "startOffsets": "12914", "endColumns": "129", "endOffsets": "13039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,506,607,880,971,1064,1156,1250,1350,1443,1538,1631,1722,2000,2404,2507,2759", "endColumns": "107,103,100,113,90,92,91,93,99,92,94,92,90,93,97,102,154,81", "endOffsets": "208,312,602,716,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,2093,2502,2657,2836"}, "to": {"startLines": "51,52,55,56,59,60,61,62,63,64,65,66,67,68,71,75,76,508", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2597,2705,2945,3046,3259,3350,3443,3535,3629,3729,3822,3917,4010,4101,4319,4631,4734,43003", "endColumns": "107,103,100,113,90,92,91,93,99,92,94,92,90,93,97,102,154,81", "endOffsets": "2700,2804,3041,3155,3345,3438,3530,3624,3724,3817,3912,4005,4096,4190,4412,4729,4884,43080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,308,390,471,572,667", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "115,174,241,303,385,466,567,662,746"}, "to": {"startLines": "248,249,250,251,252,253,254,255,256", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "21444,21509,21568,21635,21697,21779,21860,21961,22056", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "21504,21563,21630,21692,21774,21855,21956,22051,22135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,85", "endOffsets": "134,220"}, "to": {"startLines": "544,545", "startColumns": "4,4", "startOffsets": "45856,45940", "endColumns": "83,85", "endOffsets": "45935,46021"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1234,1297,1388,1457,1524,1624,1687,1752,1813,1881,1943,2001,2115,2175,2236,2293,2366,2489,2570,2662,2769,2867,2947,3095,3176,3257,3385,3474,3550,3603,3657,3723,3801,3881,3952,4034,4106,4180,4253,4323,4432,4523,4594,4684,4779,4853,4936,5029,5078,5159,5228,5314,5399,5461,5525,5588,5657,5766,5876,5973,6073,6130,6188,6268,6347,6422", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1229,1292,1383,1452,1519,1619,1682,1747,1808,1876,1938,1996,2110,2170,2231,2288,2361,2484,2565,2657,2764,2862,2942,3090,3171,3252,3380,3469,3545,3598,3652,3718,3796,3876,3947,4029,4101,4175,4248,4318,4427,4518,4589,4679,4774,4848,4931,5024,5073,5154,5223,5309,5394,5456,5520,5583,5652,5761,5871,5968,6068,6125,6183,6263,6342,6417,6493"}, "to": {"startLines": "27,118,119,120,121,122,138,139,153,221,223,275,299,307,379,380,381,382,383,384,385,386,387,388,389,390,391,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,475,509,510,521", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1409,9021,9099,9175,9259,9351,10622,10723,11711,19292,19440,23269,25070,25643,33095,33195,33258,33323,33384,33452,33514,33572,33686,33746,33807,33864,33937,34185,34266,34358,34465,34563,34643,34791,34872,34953,35081,35170,35246,35299,35353,35419,35497,35577,35648,35730,35802,35876,35949,36019,36128,36219,36290,36380,36475,36549,36632,36725,36774,36855,36924,37010,37095,37157,37221,37284,37353,37462,37572,37669,37769,37826,40900,43085,43164,44006", "endLines": "34,118,119,120,121,122,138,139,153,221,223,275,299,307,379,380,381,382,383,384,385,386,387,388,389,390,391,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,475,509,510,521", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "1769,9094,9170,9254,9346,9429,10718,10837,11783,19346,19498,23355,25134,25705,33190,33253,33318,33379,33447,33509,33567,33681,33741,33802,33859,33932,34055,34261,34353,34460,34558,34638,34786,34867,34948,35076,35165,35241,35294,35348,35414,35492,35572,35643,35725,35797,35871,35944,36014,36123,36214,36285,36375,36470,36544,36627,36720,36769,36850,36919,37005,37090,37152,37216,37279,37348,37457,37567,37664,37764,37821,37879,40975,43159,43234,44077"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-ar\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,264,30,31,32,33,34,35,36,240,250,253,255,37,2,263,243,261,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,238,57,58,242,59,60,61,62,63,64,65,66,67,68,248,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,258,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,259,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,241,168,169,170,171,172,173,174,175,176,177,178,246,245,244,252,256,179,180,181,182,183,184,254,185,186,187,239,188,260,189,190,191,192,193,194,195,196,247,197,198,257,199,200,201,202,203,251,204,205,206,207,208,209,210,211,212,213,214,215,216,262,217,218,219,220,221,222,223,224,225,226,249,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "122,180,227,277,324,373,422,469,517,570,629,679,726,775,822,18960,872,957,1010,1061,1111,1161,1237,1304,1383,1454,1519,1580,21218,1640,1681,1810,1868,1948,1991,2049,19156,19914,20270,20388,2129,57,21170,19355,21048,2168,2217,2258,2336,2392,2459,2805,3158,10467,10531,9913,9835,10594,3200,3269,3339,3508,3724,3795,3845,3885,3940,4011,4055,19049,4119,4181,19286,4223,4293,4333,4394,4530,4591,4668,4738,4799,4880,19790,4938,4991,5044,5107,5162,5205,5268,5343,5390,5658,5711,5771,6248,6745,6813,6867,6977,7035,7257,7306,7660,7717,7829,7958,8082,8140,8184,8229,8295,8377,8454,8502,8600,8688,8744,8808,8874,9115,9341,9497,9640,9718,10681,10746,20854,10799,10838,10928,10994,11052,11234,11282,11352,11408,11462,11584,11642,11764,11956,11996,12051,12089,12127,12163,12199,12274,12316,12367,12416,12484,20915,12549,12614,12667,12711,12751,12821,13188,13243,13314,13459,13531,13597,13634,13669,13706,13751,13804,13863,13931,13981,14050,14162,19211,14197,14250,14298,14384,14451,14524,14618,14709,14773,14811,14883,19620,19468,19396,20182,20637,15125,15165,15215,15266,15332,15408,20322,15448,15521,15568,19111,15610,20986,15705,15792,15881,15925,15991,16155,16226,16282,19713,16345,16398,20687,16457,16529,16614,16682,16730,20115,16803,16870,16913,16971,17014,17121,17374,17445,17491,17545,17616,17669,17723,21112,17764,17829,17995,18077,18138,18206,18256,18329,18394,18431,19858,18485,18541,18584,18623,18674,18730,18795,18842,18879,18915", "endColumns": "56,45,48,45,47,47,45,46,51,57,48,45,47,45,48,87,83,51,49,48,48,74,65,77,69,63,59,58,66,39,127,56,78,41,56,78,53,199,50,247,37,63,46,39,62,47,39,76,54,65,344,351,40,62,61,552,76,83,67,68,167,214,69,48,38,53,69,42,62,60,60,40,67,68,38,59,134,59,75,68,59,79,56,66,51,51,61,53,41,61,73,45,266,51,58,475,495,66,52,108,56,220,47,352,55,110,127,122,56,42,43,64,80,75,46,96,86,54,62,64,239,224,154,141,76,70,63,51,59,37,88,64,56,180,46,68,54,52,120,56,120,190,38,53,36,36,34,34,73,40,49,47,66,63,69,63,51,42,38,68,365,53,69,143,70,64,35,33,35,43,51,57,66,48,67,110,33,73,51,46,84,65,71,92,89,62,36,70,240,91,150,70,86,48,38,48,49,64,74,38,64,71,45,40,43,93,60,85,87,42,64,162,69,54,61,75,51,57,165,70,83,66,46,71,65,65,41,56,41,105,251,69,44,52,69,51,52,39,56,63,164,80,59,66,48,71,63,35,52,54,54,41,37,49,54,63,45,35,34,43", "endOffsets": "174,221,271,318,367,416,463,511,564,623,673,720,769,816,866,19043,951,1004,1055,1105,1155,1231,1298,1377,1448,1513,1574,1634,21280,1675,1804,1862,1942,1985,2043,2123,19205,20109,20316,20631,2162,116,21212,19390,21106,2211,2252,2330,2386,2453,2799,3152,3194,10525,10588,10461,9907,10673,3263,3333,3502,3718,3789,3839,3879,3934,4005,4049,4113,19105,4175,4217,19349,4287,4327,4388,4524,4585,4662,4732,4793,4874,4932,19852,4985,5038,5101,5156,5199,5262,5337,5384,5652,5705,5765,6242,6739,6807,6861,6971,7029,7251,7300,7654,7711,7823,7952,8076,8134,8178,8223,8289,8371,8448,8496,8594,8682,8738,8802,8868,9109,9335,9491,9634,9712,9784,10740,10793,20909,10832,10922,10988,11046,11228,11276,11346,11402,11456,11578,11636,11758,11950,11990,12045,12083,12121,12157,12193,12268,12310,12361,12410,12478,12543,20980,12608,12661,12705,12745,12815,13182,13237,13308,13453,13525,13591,13628,13663,13700,13745,13798,13857,13925,13975,14044,14156,14191,19280,14244,14292,14378,14445,14518,14612,14703,14767,14805,14877,15119,19707,19614,19462,20264,20681,15159,15209,15260,15326,15402,15442,20382,15515,15562,15604,19150,15699,21042,15786,15875,15919,15985,16149,16220,16276,16339,19784,16392,16451,20848,16523,16608,16676,16724,16797,20176,16864,16907,16965,17008,17115,17368,17439,17485,17539,17610,17663,17717,17758,21164,17823,17989,18071,18132,18200,18250,18323,18388,18425,18479,19908,18535,18578,18617,18668,18724,18789,18836,18873,18909,18954"}, "to": {"startLines": "35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,53,54,57,58,69,70,72,73,74,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,123,124,125,126,127,135,136,137,140,141,142,143,144,145,146,147,148,149,150,151,152,174,175,176,177,179,180,181,182,183,184,185,186,187,188,189,190,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,216,217,218,219,220,222,274,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,300,301,303,305,306,308,309,310,311,312,313,314,315,316,317,375,376,377,378,392,393,439,446,447,448,449,450,452,453,454,455,456,457,458,459,468,469,470,471,472,473,474,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,493,494,495,496,497,498,506,507,511,513,514,515,516,517,518,519,520,522,523,524,525,526,527,528,532,533,534,537,539,540,541,542,543,546,547,548,549,550,551,552,553,554,555,557,558,559,560,561,562,563,564", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1774,1831,1877,1926,1972,2020,2068,2114,2161,2213,2271,2320,2366,2414,2460,2509,2809,2893,3160,3210,4195,4244,4417,4483,4561,4889,4953,5013,5072,5139,5179,5307,5364,5443,5485,5542,5621,5675,5875,5926,6174,6212,6276,6323,6363,6426,6474,6514,6591,6646,6712,7057,7409,7450,7513,7575,8128,8205,8289,8357,8426,8594,8809,8879,8928,8967,9434,9504,9547,9610,9671,10444,10485,10553,10842,10881,10941,11076,11136,11212,11281,11341,11421,11478,11545,11597,11649,14056,14110,14152,14214,14388,14434,14701,14753,14812,15288,15784,15851,15904,16013,16070,16291,16532,16885,16941,17052,17180,17303,17360,17403,17447,17512,17593,17669,17716,17813,17900,17955,18018,18083,18323,18548,18703,18845,19007,19078,19142,19194,19254,19351,23204,23668,23725,23906,23953,24022,24077,24130,24251,24308,24429,24620,24659,24713,24750,24787,24822,24857,24931,24972,25022,25139,25206,25351,25527,25591,25710,25753,25792,25861,26227,26281,26351,26495,26566,26631,32929,32963,32999,33043,34060,34118,37884,38393,38461,38572,38606,38680,38813,38860,38945,39011,39083,39176,39266,39329,40138,40209,40450,40542,40693,40764,40851,40980,41019,41068,41118,41183,41258,41297,41362,41434,41480,41521,41565,41659,41720,41806,42058,42101,42166,42329,42399,42454,42875,42951,43239,43367,43533,43604,43688,43755,43802,43874,43940,44082,44124,44181,44223,44329,44581,44651,44950,45003,45073,45275,45449,45489,45546,45610,45775,46026,46086,46153,46202,46274,46338,46374,46427,46482,46537,46639,46677,46727,46782,46846,46892,46928,46963", "endColumns": "56,45,48,45,47,47,45,46,51,57,48,45,47,45,48,87,83,51,49,48,48,74,65,77,69,63,59,58,66,39,127,56,78,41,56,78,53,199,50,247,37,63,46,39,62,47,39,76,54,65,344,351,40,62,61,552,76,83,67,68,167,214,69,48,38,53,69,42,62,60,60,40,67,68,38,59,134,59,75,68,59,79,56,66,51,51,61,53,41,61,73,45,266,51,58,475,495,66,52,108,56,220,47,352,55,110,127,122,56,42,43,64,80,75,46,96,86,54,62,64,239,224,154,141,76,70,63,51,59,37,88,64,56,180,46,68,54,52,120,56,120,190,38,53,36,36,34,34,73,40,49,47,66,63,69,63,51,42,38,68,365,53,69,143,70,64,35,33,35,43,51,57,66,48,67,110,33,73,51,46,84,65,71,92,89,62,36,70,240,91,150,70,86,48,38,48,49,64,74,38,64,71,45,40,43,93,60,85,87,42,64,162,69,54,61,75,51,57,165,70,83,66,46,71,65,65,41,56,41,105,251,69,44,52,69,51,52,39,56,63,164,80,59,66,48,71,63,35,52,54,54,41,37,49,54,63,45,35,34,43", "endOffsets": "1826,1872,1921,1967,2015,2063,2109,2156,2208,2266,2315,2361,2409,2455,2504,2592,2888,2940,3205,3254,4239,4314,4478,4556,4626,4948,5008,5067,5134,5174,5302,5359,5438,5480,5537,5616,5670,5870,5921,6169,6207,6271,6318,6358,6421,6469,6509,6586,6641,6707,7052,7404,7445,7508,7570,8123,8200,8284,8352,8421,8589,8804,8874,8923,8962,9016,9499,9542,9605,9666,9727,10480,10548,10617,10876,10936,11071,11131,11207,11276,11336,11416,11473,11540,11592,11644,11706,14105,14147,14209,14283,14429,14696,14748,14807,15283,15779,15846,15899,16008,16065,16286,16334,16880,16936,17047,17175,17298,17355,17398,17442,17507,17588,17664,17711,17808,17895,17950,18013,18078,18318,18543,18698,18840,18917,19073,19137,19189,19249,19287,19435,23264,23720,23901,23948,24017,24072,24125,24246,24303,24424,24615,24654,24708,24745,24782,24817,24852,24926,24967,25017,25065,25201,25265,25416,25586,25638,25748,25787,25856,26222,26276,26346,26490,26561,26626,26662,32958,32994,33038,33090,34113,34180,37928,38456,38567,38601,38675,38727,38855,38940,39006,39078,39171,39261,39324,39361,40204,40445,40537,40688,40759,40846,40895,41014,41063,41113,41178,41253,41292,41357,41429,41475,41516,41560,41654,41715,41801,41889,42096,42161,42324,42394,42449,42511,42946,42998,43292,43528,43599,43683,43750,43797,43869,43935,44001,44119,44176,44218,44324,44576,44646,44691,44998,45068,45120,45323,45484,45541,45605,45770,45851,46081,46148,46197,46269,46333,46369,46422,46477,46532,46574,46672,46722,46777,46841,46887,46923,46958,47002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,400,516,614,719,842,979,1100,1243,1330,1435,1527,1627,1745,1871,1981,2127,2271,2408,2560,2686,2806,2929,3047,3140,3238,3361,3485,3585,3688,3796,3941,4091,4198,4300,4380,4474,4567,4684,4773,4858,4958,5037,5121,5222,5325,5424,5522,5609,5715,5815,5915,6044,6123,6224", "endColumns": "117,116,109,115,97,104,122,136,120,142,86,104,91,99,117,125,109,145,143,136,151,125,119,122,117,92,97,122,123,99,102,107,144,149,106,101,79,93,92,116,88,84,99,78,83,100,102,98,97,86,105,99,99,128,78,100,92", "endOffsets": "168,285,395,511,609,714,837,974,1095,1238,1325,1430,1522,1622,1740,1866,1976,2122,2266,2403,2555,2681,2801,2924,3042,3135,3233,3356,3480,3580,3683,3791,3936,4086,4193,4295,4375,4469,4562,4679,4768,4853,4953,5032,5116,5217,5320,5419,5517,5604,5710,5810,5910,6039,6118,6219,6312"}, "to": {"startLines": "318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "26667,26785,26902,27012,27128,27226,27331,27454,27591,27712,27855,27942,28047,28139,28239,28357,28483,28593,28739,28883,29020,29172,29298,29418,29541,29659,29752,29850,29973,30097,30197,30300,30408,30553,30703,30810,30912,30992,31086,31179,31296,31385,31470,31570,31649,31733,31834,31937,32036,32134,32221,32327,32427,32527,32656,32735,32836", "endColumns": "117,116,109,115,97,104,122,136,120,142,86,104,91,99,117,125,109,145,143,136,151,125,119,122,117,92,97,122,123,99,102,107,144,149,106,101,79,93,92,116,88,84,99,78,83,100,102,98,97,86,105,99,99,128,78,100,92", "endOffsets": "26780,26897,27007,27123,27221,27326,27449,27586,27707,27850,27937,28042,28134,28234,28352,28478,28588,28734,28878,29015,29167,29293,29413,29536,29654,29747,29845,29968,30092,30192,30295,30403,30548,30698,30805,30907,30987,31081,31174,31291,31380,31465,31565,31644,31728,31829,31932,32031,32129,32216,32322,32422,32522,32651,32730,32831,32924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,110", "endOffsets": "160,271"}, "to": {"startLines": "443,444", "startColumns": "4,4", "startOffsets": "38088,38198", "endColumns": "109,110", "endOffsets": "38193,38304"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,372,470,555,636,742,826,907,988,1071,1220,1299,1373,1449,1523", "endColumns": "88,82,94,97,84,80,105,83,80,80,82,69,78,73,75,73,120", "endOffsets": "189,272,367,465,550,631,737,821,902,983,1066,1136,1294,1368,1444,1518,1639"}, "to": {"startLines": "154,155,191,192,215,302,304,445,451,491,492,512,529,530,535,536,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11788,11877,16339,16434,18922,25270,25421,38309,38732,41894,41975,43297,44696,44775,45125,45201,45328", "endColumns": "88,82,94,97,84,80,105,83,80,80,82,69,78,73,75,73,120", "endOffsets": "11872,11955,16429,16527,19002,25346,25522,38388,38808,41970,42053,43362,44770,44844,45196,45270,45444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "178,276,277,278", "startColumns": "4,4,4,4", "startOffsets": "14288,23360,23458,23566", "endColumns": "99,97,107,101", "endOffsets": "14383,23453,23561,23663"}}]}]}