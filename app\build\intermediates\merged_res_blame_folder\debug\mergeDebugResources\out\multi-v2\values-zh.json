{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-99:/values-zh/values-zh.xml", "map": [{"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-zh\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,264,30,31,32,33,34,35,36,240,250,253,255,37,2,263,243,261,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,238,57,58,242,59,60,61,62,63,64,65,66,67,68,248,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,258,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,259,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,241,168,169,170,171,172,173,174,175,176,177,178,246,245,244,252,256,179,180,181,182,183,184,254,185,186,187,239,188,260,189,190,191,192,193,194,195,196,247,197,198,257,199,200,201,202,203,251,204,205,206,207,208,209,210,211,212,213,214,215,216,262,217,218,219,220,221,222,223,224,225,226,249,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "104,156,200,245,289,334,383,428,473,523,572,621,666,713,760,13805,807,878,931,979,1026,1075,1142,1208,1276,1343,1408,1461,15442,1507,1543,1613,1662,1725,1768,1822,13975,14544,14770,14867,1884,57,15401,14131,15300,1922,1968,2005,2061,2109,2166,2313,2463,7221,7281,6965,6895,7341,2501,2557,2611,2698,2817,2876,2914,2952,3001,3057,3098,13881,3152,3204,14077,3243,3302,3341,3390,3465,3519,3583,3646,3697,3757,14444,3809,3856,3903,3953,3999,4039,4095,4148,4191,4313,4362,4412,4607,4812,4871,4914,4981,5027,5156,5201,5364,5414,5495,5577,5660,5711,5751,5793,5850,5917,5983,6028,6096,6165,6214,6272,6331,6448,6566,6656,6736,6800,7428,7482,15147,7531,7569,7631,7682,7741,7826,7870,7924,7975,8017,8098,8143,8217,8309,8346,8394,8433,8472,8509,8546,8602,8641,8683,8722,8773,15196,8830,8888,8932,8973,9011,9075,9227,9272,9325,9407,9463,9522,9559,9594,9635,9678,9731,9783,9840,9887,9946,10015,14017,10049,10097,10142,10204,10269,10335,10408,10472,10520,10556,10611,14318,14231,14169,14696,14980,10725,10765,10821,10872,10924,10982,14813,11020,11082,11124,13934,11162,15250,11230,11298,11371,11411,11461,11553,11611,11656,14388,11705,11750,15021,11798,11856,11925,11981,12022,14640,12076,12131,12173,12225,12266,12353,12486,12537,12580,12628,12677,12722,12766,15352,12804,12858,12972,13032,13084,13140,13187,13245,13293,13332,14497,13376,13420,13460,13502,13546,13593,13646,13689,13724,13760", "endColumns": "50,42,43,42,43,47,43,43,48,47,47,43,45,45,45,74,69,51,46,45,47,65,64,66,65,63,51,44,51,34,68,47,61,41,52,60,40,94,41,111,36,45,39,36,50,44,35,54,46,55,145,148,36,58,58,254,68,83,54,52,85,117,57,36,36,47,54,39,52,51,50,37,52,57,37,47,73,52,62,61,49,58,50,51,45,45,48,44,38,54,51,41,120,47,48,193,203,57,41,65,44,127,43,161,48,79,80,81,49,38,40,55,65,64,43,66,67,47,56,57,115,116,88,78,62,48,52,47,47,36,60,49,57,83,42,52,49,40,79,43,72,90,35,46,37,37,35,35,54,37,40,37,49,55,52,56,42,39,36,62,150,43,51,80,54,57,35,33,39,41,51,50,55,45,57,67,32,58,46,43,60,63,64,71,62,46,34,53,112,68,85,60,72,39,38,54,49,50,56,36,52,60,40,36,39,66,48,66,71,38,48,90,56,43,47,54,43,46,124,56,67,54,39,52,54,53,40,50,39,85,131,49,41,46,47,43,42,36,47,52,112,58,50,54,45,56,46,37,42,45,42,38,40,42,45,51,41,33,34,43", "endOffsets": "150,194,239,283,328,377,422,467,517,566,615,660,707,754,801,13875,872,925,973,1020,1069,1136,1202,1270,1337,1402,1455,1501,15489,1537,1607,1656,1719,1762,1816,1878,14011,14634,14807,14974,1916,98,15436,14163,15346,1962,1999,2055,2103,2160,2307,2457,2495,7275,7335,7215,6959,7420,2551,2605,2692,2811,2870,2908,2946,2995,3051,3092,3146,13928,3198,3237,14125,3296,3335,3384,3459,3513,3577,3640,3691,3751,3803,14491,3850,3897,3947,3993,4033,4089,4142,4185,4307,4356,4406,4601,4806,4865,4908,4975,5021,5150,5195,5358,5408,5489,5571,5654,5705,5745,5787,5844,5911,5977,6022,6090,6159,6208,6266,6325,6442,6560,6650,6730,6794,6844,7476,7525,15190,7563,7625,7676,7735,7820,7864,7918,7969,8011,8092,8137,8211,8303,8340,8388,8427,8466,8503,8540,8596,8635,8677,8716,8767,8824,15244,8882,8926,8967,9005,9069,9221,9266,9319,9401,9457,9516,9553,9588,9629,9672,9725,9777,9834,9881,9940,10009,10043,14071,10091,10136,10198,10263,10329,10402,10466,10514,10550,10605,10719,14382,14312,14225,14764,15015,10759,10815,10866,10918,10976,11014,14861,11076,11118,11156,13969,11224,15294,11292,11365,11405,11455,11547,11605,11650,11699,14438,11744,11792,15141,11850,11919,11975,12016,12070,14690,12125,12167,12219,12260,12347,12480,12531,12574,12622,12671,12716,12760,12798,15395,12852,12966,13026,13078,13134,13181,13239,13287,13326,13370,14538,13414,13454,13496,13540,13587,13640,13683,13718,13754,13799"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,106,149,193,236,280,328,372,416,465,513,561,605,651,697,743,818,888,940,987,1033,1081,1147,1212,1279,1345,1409,1461,1506,1558,1593,1662,1710,1772,1814,1867,1928,1969,2064,2106,2218,2255,2301,2889,2926,2977,3022,3058,3113,3160,3216,3362,3511,3548,3607,3666,3921,3990,4074,4129,4182,4268,4386,4444,4481,4518,4566,4621,4661,4714,4766,4817,4855,4908,4966,5004,5052,5126,5179,5242,5304,5354,5413,5464,5516,5562,5608,5657,5702,5741,5796,5848,5890,6011,6059,6108,6302,6506,6564,6606,6672,6717,6845,6889,7051,7100,7180,7261,7343,7393,7432,7473,7529,7595,7660,7704,7771,7839,7887,7944,8002,8118,8235,8324,8403,8466,8515,8568,8616,8664,8701,8762,8812,8870,8954,8997,9050,9100,9141,9221,9265,9338,9429,9465,9512,9550,9588,9624,9660,9715,9753,9794,9832,9882,9938,9991,10048,10091,10131,10168,10231,10382,10426,10478,10559,10614,10672,10708,10742,10782,10824,10876,10927,10983,11029,11087,11155,11188,11247,11294,11338,11399,11463,11528,11600,11663,11710,11745,11799,11912,11981,12067,12128,12201,12241,12280,12335,12385,12436,12493,12530,12583,12644,12685,12722,12762,12829,12878,12945,13017,13056,13105,13196,13253,13297,13345,13400,13444,13491,13616,13673,13741,13796,13836,13889,13944,13998,14039,14090,14130,14216,14348,14398,14440,14487,14535,14579,14622,14659,14707,14760,14873,14932,14983,15038,15084,15141,15188,15226,15269,15315,15358,15397,15438,15481,15527,15579,15621,15655,15690", "endColumns": "50,42,43,42,43,47,43,43,48,47,47,43,45,45,45,74,69,51,46,45,47,65,64,66,65,63,51,44,51,34,68,47,61,41,52,60,40,94,41,111,36,45,39,36,50,44,35,54,46,55,145,148,36,58,58,254,68,83,54,52,85,117,57,36,36,47,54,39,52,51,50,37,52,57,37,47,73,52,62,61,49,58,50,51,45,45,48,44,38,54,51,41,120,47,48,193,203,57,41,65,44,127,43,161,48,79,80,81,49,38,40,55,65,64,43,66,67,47,56,57,115,116,88,78,62,48,52,47,47,36,60,49,57,83,42,52,49,40,79,43,72,90,35,46,37,37,35,35,54,37,40,37,49,55,52,56,42,39,36,62,150,43,51,80,54,57,35,33,39,41,51,50,55,45,57,67,32,58,46,43,60,63,64,71,62,46,34,53,112,68,85,60,72,39,38,54,49,50,56,36,52,60,40,36,39,66,48,66,71,38,48,90,56,43,47,54,43,46,124,56,67,54,39,52,54,53,40,50,39,85,131,49,41,46,47,43,42,36,47,52,112,58,50,54,45,56,46,37,42,45,42,38,40,42,45,51,41,33,34,43", "endOffsets": "101,144,188,231,275,323,367,411,460,508,556,600,646,692,738,813,883,935,982,1028,1076,1142,1207,1274,1340,1404,1456,1501,1553,1588,1657,1705,1767,1809,1862,1923,1964,2059,2101,2213,2250,2296,2336,2921,2972,3017,3053,3108,3155,3211,3357,3506,3543,3602,3661,3916,3985,4069,4124,4177,4263,4381,4439,4476,4513,4561,4616,4656,4709,4761,4812,4850,4903,4961,4999,5047,5121,5174,5237,5299,5349,5408,5459,5511,5557,5603,5652,5697,5736,5791,5843,5885,6006,6054,6103,6297,6501,6559,6601,6667,6712,6840,6884,7046,7095,7175,7256,7338,7388,7427,7468,7524,7590,7655,7699,7766,7834,7882,7939,7997,8113,8230,8319,8398,8461,8510,8563,8611,8659,8696,8757,8807,8865,8949,8992,9045,9095,9136,9216,9260,9333,9424,9460,9507,9545,9583,9619,9655,9710,9748,9789,9827,9877,9933,9986,10043,10086,10126,10163,10226,10377,10421,10473,10554,10609,10667,10703,10737,10777,10819,10871,10922,10978,11024,11082,11150,11183,11242,11289,11333,11394,11458,11523,11595,11658,11705,11740,11794,11907,11976,12062,12123,12196,12236,12275,12330,12380,12431,12488,12525,12578,12639,12680,12717,12757,12824,12873,12940,13012,13051,13100,13191,13248,13292,13340,13395,13439,13486,13611,13668,13736,13791,13831,13884,13939,13993,14034,14085,14125,14211,14343,14393,14435,14482,14530,14574,14617,14654,14702,14755,14868,14927,14978,15033,15079,15136,15183,15221,15264,15310,15353,15392,15433,15476,15522,15574,15616,15650,15685,15729"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d148dadd36a9aeec86630cf6fa0b2687\\transformed\\jetified-applovin-sdk-13.3.1\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,131,185,246,341,417,479,543", "endColumns": "75,53,60,94,75,61,63,59", "endOffsets": "126,180,241,336,412,474,538,598"}, "to": {"startLines": "45,46,47,48,49,50,51,52", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2341,2417,2471,2532,2627,2703,2765,2829", "endColumns": "75,53,60,94,75,61,63,59", "endOffsets": "2412,2466,2527,2622,2698,2760,2824,2884"}}]}]}