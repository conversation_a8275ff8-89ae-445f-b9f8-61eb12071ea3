package com.tqhit.battery.one.fragment.main.others;

import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager;
import com.tqhit.battery.one.features.navigation.AppNavigator;
import com.tqhit.battery.one.features.navigation.DynamicNavigationManager;
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class OthersFragment_MembersInjector implements MembersInjector<OthersFragment> {
  private final Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider;

  private final Provider<DynamicNavigationManager> dynamicNavigationManagerProvider;

  private final Provider<AppNavigator> appNavigatorProvider;

  private final Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider;

  public OthersFragment_MembersInjector(Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<DynamicNavigationManager> dynamicNavigationManagerProvider,
      Provider<AppNavigator> appNavigatorProvider,
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider) {
    this.coreBatteryStatsProvider = coreBatteryStatsProvider;
    this.dynamicNavigationManagerProvider = dynamicNavigationManagerProvider;
    this.appNavigatorProvider = appNavigatorProvider;
    this.applovinInterstitialAdManagerProvider = applovinInterstitialAdManagerProvider;
  }

  public static MembersInjector<OthersFragment> create(
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<DynamicNavigationManager> dynamicNavigationManagerProvider,
      Provider<AppNavigator> appNavigatorProvider,
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider) {
    return new OthersFragment_MembersInjector(coreBatteryStatsProvider, dynamicNavigationManagerProvider, appNavigatorProvider, applovinInterstitialAdManagerProvider);
  }

  @Override
  public void injectMembers(OthersFragment instance) {
    injectCoreBatteryStatsProvider(instance, coreBatteryStatsProvider.get());
    injectDynamicNavigationManager(instance, dynamicNavigationManagerProvider.get());
    injectAppNavigator(instance, appNavigatorProvider.get());
    injectApplovinInterstitialAdManager(instance, applovinInterstitialAdManagerProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.others.OthersFragment.coreBatteryStatsProvider")
  public static void injectCoreBatteryStatsProvider(OthersFragment instance,
      CoreBatteryStatsProvider coreBatteryStatsProvider) {
    instance.coreBatteryStatsProvider = coreBatteryStatsProvider;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.others.OthersFragment.dynamicNavigationManager")
  public static void injectDynamicNavigationManager(OthersFragment instance,
      DynamicNavigationManager dynamicNavigationManager) {
    instance.dynamicNavigationManager = dynamicNavigationManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.others.OthersFragment.appNavigator")
  public static void injectAppNavigator(OthersFragment instance, AppNavigator appNavigator) {
    instance.appNavigator = appNavigator;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.others.OthersFragment.applovinInterstitialAdManager")
  public static void injectApplovinInterstitialAdManager(OthersFragment instance,
      ApplovinInterstitialAdManager applovinInterstitialAdManager) {
    instance.applovinInterstitialAdManager = applovinInterstitialAdManager;
  }
}
